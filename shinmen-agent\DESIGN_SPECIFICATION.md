# Interactive AI Agent Website with Custom API Integration - Design Specification

## Executive Summary

This document outlines the comprehensive design specification for an Interactive AI Agent Website that extends the existing Shinmen platform with custom API integration capabilities. The platform will enable users to interact with multiple specialized AI agents, submit complex prompts, preview generated code, and dynamically integrate custom APIs to extend agent capabilities.

## Table of Contents

1. [Core Platform Features](#1-core-platform-features)
2. [Custom API Integration System](#2-custom-api-integration-system)
3. [Technical Architecture](#3-technical-architecture)
4. [User Interface Design](#4-user-interface-design)
5. [Security Framework](#5-security-framework)
6. [Data Models & Database Schema](#6-data-models--database-schema)
7. [Implementation Roadmap](#7-implementation-roadmap)
8. [Success Metrics](#8-success-metrics)

---

## 1. Core Platform Features

### 1.1 Multi-Agent Chat Interface

**Primary Features:**
- **Agent Selection Hub**: Dynamic interface showcasing all 12 specialized agents with capability descriptions
- **Context-Aware Conversations**: Maintain conversation history and context across agent switches
- **Real-time Streaming**: WebSocket-based message streaming with typing indicators
- **Rich Message Display**: Support for formatted text, code blocks, images, and interactive elements

**Agent Ecosystem Integration:**
Building on the existing 12-agent foundation:
- **agent.md**: Pair Programming Agent with codebase search and workspace state management
- **agent2.md**: DataForge - Database/SQL Expert with query optimization
- **agent3.md**: CloudOps - Infrastructure Expert with deployment automation
- **agent4.md**: Devin Software Engineer with autonomous development capabilities
- **agent5.md**: Lovable AI Editor with React/web development focus
- **agent6.md**: Bolt Expert with WebContainer and instant deployment
- **agent7.md**: Cline Software Engineer with advanced MCP integration
- **agent8.md**: Roo Expert Engineer with maintainability focus
- **agent9.md**: Replit Expert with platform-specific workflows
- **agent10.md**: Trae AI with web integration and citations
- **agent11.md**: v0 Vercel Assistant with Next.js specialization
- **agent12.md**: GitHub Copilot with comprehensive code analysis

### 1.2 Advanced Code Generation & Preview

**Code Generation Pipeline:**
```mermaid
graph TD
    A[User Prompt] --> B[Agent Processing]
    B --> C[Code Generation]
    C --> D[Syntax Validation]
    D --> E[Security Scan]
    E --> F[Live Preview]
    F --> G[User Approval]
    G --> H[Project Integration]
```

**Preview System Features:**
- **Sandboxed Execution**: Secure iframe-based preview environment
- **Multi-Language Support**: HTML/CSS/JavaScript, React components, Python scripts
- **Real-time Updates**: Hot reload for code modifications
- **Error Handling**: Comprehensive error display with debugging information
- **Export Options**: Download as files, create GitHub repository, deploy to platforms

### 1.3 Project Management System

**Workspace Features:**
- **File Tree Navigation**: Hierarchical project structure with drag-and-drop
- **Multi-tab Editing**: Monaco Editor integration with IntelliSense
- **Version Control**: Git integration with commit history and branching
- **Template Library**: Pre-built project templates for common frameworks
- **Collaboration**: Real-time collaborative editing with conflict resolution

---

## 2. Custom API Integration System

### 2.1 API Management Interface

**Configuration Dashboard:**
```typescript
interface APIConfiguration {
  id: string;
  name: string;                    // User-friendly name (e.g., "OpenWeatherMap")
  provider: string;                // Provider identifier
  apiKey: string;                  // Encrypted storage
  modelName?: string;              // For AI model APIs (e.g., "gpt-4o")
  baseUrl: string;                 // API endpoint URL
  authMethod: AuthMethod;          // Bearer, API Key, OAuth
  requestSchema: JSONSchema;       // Request structure template
  responseParser: ResponseParser;   // Response extraction rules
  rateLimit: RateLimitConfig;      // Usage constraints
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface AuthMethod {
  type: 'bearer' | 'apikey' | 'oauth' | 'basic';
  headerName?: string;             // For API key in header
  paramName?: string;              // For API key in query params
  oauthConfig?: OAuthConfiguration;
}

interface RateLimitConfig {
  requestsPerMinute: number;
  requestsPerHour: number;
  requestsPerDay: number;
  burstLimit: number;
}
```

**API Integration Workflow:**
1. **Discovery**: Auto-detect API capabilities through OpenAPI specs
2. **Configuration**: Guided setup with validation and testing
3. **Authentication**: Secure credential storage with encryption
4. **Testing**: Built-in API testing with sample requests
5. **Deployment**: Seamless integration with agent workflows

### 2.2 Dynamic API Invocation

**Agent-API Bridge:**
```typescript
class APIBridge {
  async invokeAPI(
    apiConfig: APIConfiguration,
    request: APIRequest,
    context: AgentContext
  ): Promise<APIResponse> {
    // Rate limiting check
    await this.checkRateLimit(apiConfig.id, context.userId);
    
    // Request construction
    const httpRequest = this.buildRequest(apiConfig, request);
    
    // Secure execution
    const response = await this.executeRequest(httpRequest);
    
    // Response parsing
    return this.parseResponse(response, apiConfig.responseParser);
  }
}
```

**Function Calling Integration:**
- **Automatic Discovery**: Agents automatically detect available APIs
- **Smart Routing**: Context-aware API selection based on user intent
- **Error Handling**: Graceful fallbacks and retry mechanisms
- **Usage Tracking**: Comprehensive analytics and cost monitoring

---

## 3. Technical Architecture

### 3.1 System Architecture Overview

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[React Chat Interface]
        Editor[Monaco Code Editor]
        Preview[Sandboxed Preview]
        Dashboard[API Management]
    end
    
    subgraph "API Gateway"
        Gateway[Express.js Gateway]
        Auth[JWT Authentication]
        RateLimit[Rate Limiting]
        Validation[Input Validation]
    end
    
    subgraph "Core Services"
        Orchestrator[Agent Orchestrator]
        APIBridge[API Integration Bridge]
        CodeEngine[Code Generation Engine]
        ProjectManager[Project Management]
    end
    
    subgraph "AI Integration"
        ChutesAI[Chutes AI Client]
        CustomAPIs[Custom API Clients]
        ModelRouter[Model Router]
    end
    
    subgraph "Data Layer"
        PostgreSQL[(PostgreSQL)]
        Redis[(Redis Cache)]
        FileStorage[(File Storage)]
        VectorDB[(Vector Database)]
    end
    
    UI --> Gateway
    Editor --> Gateway
    Preview --> Gateway
    Dashboard --> Gateway
    
    Gateway --> Orchestrator
    Gateway --> APIBridge
    Gateway --> CodeEngine
    Gateway --> ProjectManager
    
    Orchestrator --> ChutesAI
    APIBridge --> CustomAPIs
    CodeEngine --> ModelRouter
    
    Orchestrator --> PostgreSQL
    APIBridge --> Redis
    ProjectManager --> FileStorage
    ChutesAI --> VectorDB
```

### 3.2 Technology Stack

**Frontend Stack:**
- **Framework**: React 18 with TypeScript
- **UI Components**: Tailwind CSS + shadcn/ui
- **State Management**: Zustand for global state
- **Code Editor**: Monaco Editor with language support
- **Build Tool**: Vite with hot module replacement
- **Testing**: Vitest + React Testing Library

**Backend Stack:**
- **Runtime**: Node.js 20+ with TypeScript
- **Framework**: Express.js with Helmet security
- **Database**: PostgreSQL 15+ with Prisma ORM
- **Caching**: Redis for session and API response caching
- **Authentication**: JWT with refresh token rotation
- **API Documentation**: OpenAPI 3.0 with Swagger UI

**Infrastructure:**
- **Containerization**: Docker with multi-stage builds
- **Orchestration**: Kubernetes for production deployment
- **Monitoring**: Prometheus + Grafana for metrics
- **Logging**: Structured logging with Winston
- **Security**: OWASP security headers and input validation

---

## 4. User Interface Design

### 4.1 Main Chat Interface

**Layout Structure:**
```
┌─────────────────────────────────────────────────────────────┐
│ Header: Logo | Agent Selector | User Menu | Settings        │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────┐ │
│ │ Agent Panel     │ │ Chat Messages                       │ │
│ │ - Active Agent  │ │ ┌─────────────────────────────────┐ │ │
│ │ - Capabilities  │ │ │ User: Generate a React component│ │ │
│ │ - Status        │ │ │ for a todo list                 │ │ │
│ │ - Switch Button │ │ └─────────────────────────────────┘ │ │
│ │                 │ │ ┌─────────────────────────────────┐ │ │
│ │ API Status      │ │ │ Agent: I'll create a modern     │ │ │
│ │ - Connected     │ │ │ React todo component...         │ │ │
│ │ - Rate Limits   │ │ │ [Code Block with Preview]       │ │ │
│ │ - Usage Stats   │ │ └─────────────────────────────────┘ │ │
│ └─────────────────┘ └─────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Input: [Type your message...] [Attach] [Send] [Voice]       │
└─────────────────────────────────────────────────────────────┘
```

**Key UI Components:**

1. **Agent Selector Dropdown:**
   - Visual agent cards with icons and descriptions
   - Capability tags and specialization indicators
   - Quick switch with context preservation
   - Agent status and availability indicators

2. **Message Display:**
   - Rich text formatting with markdown support
   - Syntax-highlighted code blocks with copy functionality
   - Inline code preview with expand/collapse
   - File attachments and image display
   - Reaction and feedback buttons

3. **Code Preview Panel:**
   - Tabbed interface for multiple files
   - Live preview with error console
   - Download and export options
   - Version history and rollback

### 4.2 API Management Dashboard

**Configuration Interface:**
```
┌─────────────────────────────────────────────────────────────┐
│ API Management Dashboard                                    │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────┐ │
│ │ API List        │ │ Configuration Panel                 │ │
│ │ ┌─────────────┐ │ │ ┌─────────────────────────────────┐ │ │
│ │ │ OpenAI GPT  │ │ │ │ API Name: [OpenWeatherMap]      │ │ │
│ │ │ ● Active    │ │ │ │ Provider: [Weather Service]     │ │ │
│ │ └─────────────┘ │ │ │ API Key: [●●●●●●●●●●●●] [Test]   │ │ │
│ │ ┌─────────────┐ │ │ │ Base URL: [api.openweather...]  │ │ │
│ │ │ Weather API │ │ │ │ Model: [N/A]                    │ │ │
│ │ │ ● Active    │ │ │ │ Auth Method: [API Key]          │ │ │
│ │ └─────────────┘ │ │ │ Rate Limit: [1000/hour]         │ │ │
│ │ ┌─────────────┐ │ │ │ [Save] [Test Connection] [Del]  │ │ │
│ │ │ + Add New   │ │ │ └─────────────────────────────────┘ │ │
│ │ └─────────────┘ │ │ ┌─────────────────────────────────┐ │ │
│ └─────────────────┘ │ │ Usage Analytics                 │ │ │
│                     │ │ - Requests today: 45/1000       │ │ │
│                     │ │ - Success rate: 98.2%           │ │ │
│                     │ │ - Avg response: 245ms           │ │ │
│                     │ │ └─────────────────────────────────┘ │ │
│                     └─────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 4.3 Responsive Design Considerations

**Mobile Optimization:**
- Collapsible sidebar for agent selection
- Touch-optimized code editor with zoom
- Swipe gestures for navigation
- Adaptive layout for different screen sizes
- Progressive Web App (PWA) capabilities

**Accessibility Features:**
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode
- Customizable font sizes

---

## 5. Security Framework

### 5.1 API Key Security

**Encryption Strategy:**
```typescript
class SecureCredentialManager {
  private encryptionKey: string;
  
  async storeAPIKey(userId: string, apiId: string, apiKey: string): Promise<void> {
    const encrypted = await this.encrypt(apiKey);
    await this.database.apiCredentials.create({
      userId,
      apiId,
      encryptedKey: encrypted,
      keyHash: this.hash(apiKey), // For validation without decryption
      createdAt: new Date()
    });
  }
  
  async retrieveAPIKey(userId: string, apiId: string): Promise<string> {
    const credential = await this.database.apiCredentials.findUnique({
      where: { userId_apiId: { userId, apiId } }
    });
    
    if (!credential) throw new Error('API key not found');
    return await this.decrypt(credential.encryptedKey);
  }
  
  private async encrypt(data: string): Promise<string> {
    // AES-256-GCM encryption implementation
  }
  
  private async decrypt(encryptedData: string): Promise<string> {
    // AES-256-GCM decryption implementation
  }
}
```

**Security Measures:**
- **Server-side Storage**: All API keys stored encrypted on server
- **Zero Client Exposure**: API keys never transmitted to frontend
- **Key Rotation**: Automatic key rotation with user notification
- **Access Logging**: Comprehensive audit trail for API key usage
- **Permission Scoping**: Granular permissions per API integration

### 5.2 Code Preview Sandboxing

**Sandbox Implementation:**
```typescript
class SecureCodePreview {
  private sandboxConfig = {
    allowedDomains: ['localhost', 'preview.shinmen.ai'],
    blockedAPIs: ['fetch', 'XMLHttpRequest', 'WebSocket'],
    memoryLimit: '50MB',
    executionTimeout: 5000,
    allowedFileTypes: ['.html', '.css', '.js', '.jsx', '.ts', '.tsx']
  };
  
  async createSandbox(code: string, language: string): Promise<SandboxResult> {
    // Content Security Policy enforcement
    const csp = this.generateCSP();
    
    // Code sanitization
    const sanitizedCode = await this.sanitizeCode(code, language);
    
    // Iframe sandbox creation
    const sandbox = this.createIframeSandbox(sanitizedCode, csp);
    
    return {
      sandboxId: sandbox.id,
      previewUrl: sandbox.url,
      securityLevel: 'high'
    };
  }
}
```

### 5.3 Input Validation & Sanitization

**Validation Pipeline:**
- **Schema Validation**: JSON Schema validation for all API inputs
- **SQL Injection Prevention**: Parameterized queries and ORM usage
- **XSS Protection**: Content sanitization and CSP headers
- **Rate Limiting**: Per-user and per-IP rate limiting
- **CSRF Protection**: Token-based CSRF protection

---

## 6. Data Models & Database Schema

### 6.1 Core Entity Relationships

```sql
-- API Configurations
CREATE TABLE api_configurations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  provider VARCHAR(100) NOT NULL,
  base_url VARCHAR(500) NOT NULL,
  auth_method VARCHAR(50) NOT NULL,
  model_name VARCHAR(100),
  request_schema JSONB,
  response_parser JSONB,
  rate_limit_config JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Encrypted API Credentials
CREATE TABLE api_credentials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  api_id UUID REFERENCES api_configurations(id) ON DELETE CASCADE,
  encrypted_key TEXT NOT NULL,
  key_hash VARCHAR(64) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  last_used TIMESTAMP,
  UNIQUE(user_id, api_id)
);

-- API Usage Analytics
CREATE TABLE api_usage_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  api_id UUID REFERENCES api_configurations(id),
  agent_id VARCHAR(50),
  request_data JSONB,
  response_data JSONB,
  status_code INTEGER,
  execution_time_ms INTEGER,
  tokens_used INTEGER,
  cost_cents INTEGER,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Code Preview Sessions
CREATE TABLE code_previews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES chat_sessions(id),
  code_content TEXT NOT NULL,
  language VARCHAR(50) NOT NULL,
  sandbox_url VARCHAR(500),
  security_level VARCHAR(20) DEFAULT 'high',
  created_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP DEFAULT NOW() + INTERVAL '24 hours'
);
```

### 6.2 Agent Configuration Schema

```typescript
interface ExtendedAgentConfig {
  id: string;
  name: string;
  description: string;
  capabilities: AgentCapability[];
  tools: ToolDefinition[];
  apiIntegrations: APIIntegration[];
  systemPrompt: string;
  modelConfig: ModelConfiguration;
  securityLevel: 'low' | 'medium' | 'high';
  rateLimits: RateLimitConfig;
}

interface APIIntegration {
  apiId: string;
  triggerPatterns: string[];        // Regex patterns for auto-invocation
  requiredPermissions: string[];
  fallbackBehavior: 'error' | 'skip' | 'alternative';
}
```

---

## 7. Implementation Roadmap

### Phase 1: Foundation Enhancement (Weeks 1-4)
**Deliverables:**
- [ ] Enhanced chat interface with agent switching
- [ ] Basic API configuration dashboard
- [ ] Secure credential storage system
- [ ] Code preview sandbox implementation

### Phase 2: API Integration Core (Weeks 5-8)
**Deliverables:**
- [ ] Dynamic API invocation system
- [ ] Function calling integration with agents
- [ ] Rate limiting and usage tracking
- [ ] Error handling and fallback mechanisms

### Phase 3: Advanced Features (Weeks 9-12)
**Deliverables:**
- [ ] Advanced code generation pipeline
- [ ] Multi-language preview support
- [ ] Project management system
- [ ] Collaborative features

### Phase 4: Production Readiness (Weeks 13-16)
**Deliverables:**
- [ ] Security audit and penetration testing
- [ ] Performance optimization
- [ ] Comprehensive documentation
- [ ] Beta user testing and feedback integration

---

## 8. Success Metrics

### 8.1 Technical KPIs
- **Response Time**: <500ms for agent responses
- **API Integration Success**: >95% successful API calls
- **Code Preview Load Time**: <2s for sandbox creation
- **Concurrent Users**: Support for 1000+ simultaneous users
- **Uptime**: 99.9% availability SLA

### 8.2 User Experience Metrics
- **Agent Switch Success**: >98% context preservation
- **Code Generation Accuracy**: >90% syntactically correct
- **User Satisfaction**: >4.5/5 rating
- **Task Completion Rate**: >85% for core workflows
- **API Integration Adoption**: >60% of users configure custom APIs

### 8.3 Security Metrics
- **Zero Critical Vulnerabilities**: OWASP Top 10 compliance
- **API Key Security**: Zero unauthorized access incidents
- **Sandbox Escape Rate**: <0.01% of preview sessions
- **Data Breach Prevention**: 100% encryption compliance

---

## 9. Wireframes & UI Mockups

### 9.1 Main Chat Interface Wireframe

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ SHINMEN AI PLATFORM                    [🔔] [👤] [⚙️]                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────┐ ┌─────────────┐ │
│ │ AGENT SELECTOR  │ │ CONVERSATION AREA                   │ │ PROJECT     │ │
│ │                 │ │                                     │ │ EXPLORER    │ │
│ │ 🤖 Pair Prog    │ │ ┌─────────────────────────────────┐ │ │             │ │
│ │ ● Active        │ │ │ User: Create a React todo app   │ │ │ 📁 my-app   │ │
│ │                 │ │ │ with TypeScript                 │ │ │ ├── src/    │ │
│ │ 🗄️ DataForge    │ │ └─────────────────────────────────┘ │ │ ├── public/ │ │
│ │ ○ Available     │ │                                     │ │ └── README  │ │
│ │                 │ │ ┌─────────────────────────────────┐ │ │             │ │
│ │ ☁️ CloudOps     │ │ │ Agent: I'll create a modern     │ │ │ [+ New]     │ │
│ │ ○ Available     │ │ │ TypeScript React todo app...    │ │ │ [Import]    │ │
│ │                 │ │ │                                 │ │ │ [Export]    │ │
│ │ [Switch Agent]  │ │ │ ```typescript                   │ │ │             │ │
│ │                 │ │ │ interface Todo {                │ │ │             │ │
│ │ API STATUS      │ │ │   id: string;                   │ │ │             │ │
│ │ ┌─────────────┐ │ │ │   text: string;                 │ │ │             │ │
│ │ │ OpenAI ✅   │ │ │ │   completed: boolean;           │ │ │             │ │
│ │ │ Weather ✅  │ │ │ │ }                               │ │ │             │ │
│ │ │ Custom ❌   │ │ │ │ ```                             │ │ │             │ │
│ │ └─────────────┘ │ │ │                                 │ │ │             │ │
│ │                 │ │ │ [📋 Copy] [👁️ Preview] [💾 Save] │ │ │             │ │
│ │ [Manage APIs]   │ │ └─────────────────────────────────┘ │ │             │ │
│ └─────────────────┘ └─────────────────────────────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│ 💬 [Type your message...] [📎] [🎤] [🚀 Send]                              │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 9.2 API Management Dashboard Wireframe

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ API MANAGEMENT DASHBOARD                                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────┐ ┌─────────────────────────────────────────┐ │
│ │ CONFIGURED APIS             │ │ API CONFIGURATION                       │ │
│ │                             │ │                                         │ │
│ │ ┌─────────────────────────┐ │ │ ┌─────────────────────────────────────┐ │ │
│ │ │ 🤖 OpenAI GPT-4         │ │ │ │ API Name: [OpenWeatherMap API]      │ │ │
│ │ │ Status: ✅ Active       │ │ │ │ Provider: [Weather Service]         │ │ │
│ │ │ Usage: 1,234/10,000     │ │ │ │ Base URL: [api.openweathermap.org]  │ │ │
│ │ │ [Edit] [Test] [Delete]  │ │ │ │ API Key: [●●●●●●●●●●●●●●●●] [👁️]    │ │ │
│ │ └─────────────────────────┘ │ │ │ Model Name: [N/A - Weather Service] │ │ │
│ │                             │ │ │ Auth Method: [API Key in Header]    │ │ │
│ │ ┌─────────────────────────┐ │ │ │ Rate Limits:                        │ │ │
│ │ │ 🌤️ Weather API          │ │ │ │ ├── Per Minute: [60]                │ │ │
│ │ │ Status: ✅ Active       │ │ │ │ ├── Per Hour: [1000]               │ │ │
│ │ │ Usage: 45/1,000         │ │ │ │ └── Per Day: [10000]               │ │ │
│ │ │ [Edit] [Test] [Delete]  │ │ │ │                                     │ │ │
│ │ └─────────────────────────┘ │ │ │ [🧪 Test Connection] [💾 Save]      │ │ │
│ │                             │ │ │ [🗑️ Delete] [📋 Copy Config]        │ │ │
│ │ ┌─────────────────────────┐ │ │ └─────────────────────────────────────┘ │ │
│ │ │ ➕ Add New API          │ │ │                                         │ │
│ │ └─────────────────────────┘ │ │ ┌─────────────────────────────────────┐ │ │
│ │                             │ │ │ USAGE ANALYTICS                     │ │ │
│ │ QUICK ACTIONS               │ │ │                                     │ │ │
│ │ ┌─────────────────────────┐ │ │ │ 📊 Requests Today: 145/1,000        │ │ │
│ │ │ 📥 Import from OpenAPI  │ │ │ │ ⚡ Avg Response Time: 245ms         │ │ │
│ │ │ 🔗 Connect to Zapier    │ │ │ │ ✅ Success Rate: 98.7%              │ │ │
│ │ │ 📋 Export Configuration │ │ │ │ 💰 Estimated Cost: $2.45           │ │ │
│ │ │ 🔄 Sync with GitHub     │ │ │ │                                     │ │ │
│ │ └─────────────────────────┘ │ │ │ [📈 View Detailed Analytics]        │ │ │
│ └─────────────────────────────┘ └─────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 9.3 Code Preview Interface Wireframe

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ CODE PREVIEW & EDITOR                                                       │
├─────────────────────────────────────────────────────────────────────────────┤
│ [📁 Files] [🔍 Search] [⚙️ Settings] [🚀 Deploy] [💾 Save] [📤 Export]      │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────┐ ┌─────────────┐ │
│ │ FILE EXPLORER   │ │ CODE EDITOR                         │ │ LIVE        │ │
│ │                 │ │                                     │ │ PREVIEW     │ │
│ │ 📁 src/         │ │ [App.tsx] [styles.css] [+]          │ │             │ │
│ │ ├── App.tsx ●   │ │ ┌─────────────────────────────────┐ │ │ ┌─────────┐ │ │
│ │ ├── index.tsx   │ │ │ 1  import React, { useState }   │ │ │ │ TODO    │ │ │
│ │ └── styles.css  │ │ │ 2  from 'react';                │ │ │ │ APP     │ │ │
│ │                 │ │ │ 3                               │ │ │ │         │ │ │
│ │ 📁 public/      │ │ │ 4  interface Todo {             │ │ │ │ ┌─────┐ │ │ │
│ │ ├── index.html  │ │ │ 5    id: string;                │ │ │ │ │ Add │ │ │ │
│ │ └── favicon.ico │ │ │ 6    text: string;              │ │ │ │ └─────┘ │ │ │
│ │                 │ │ │ 7    completed: boolean;        │ │ │ │         │ │ │
│ │ 📄 package.json │ │ │ 8  }                            │ │ │ │ ☐ Task1 │ │ │
│ │ 📄 README.md    │ │ │ 9                               │ │ │ │ ☑ Task2 │ │ │
│ │                 │ │ │ 10 const TodoApp: React.FC =   │ │ │ │ ☐ Task3 │ │ │
│ │ [+ New File]    │ │ │ 11   () => {                    │ │ │ └─────────┘ │ │
│ │ [📁 New Folder] │ │ │ 12   const [todos, setTodos]    │ │ │             │ │
│ │                 │ │ │ 13     = useState<Todo[]>([]);  │ │ │ [🔄 Refresh] │ │
│ │ TEMPLATES       │ │ │ 14                              │ │ │ [📱 Mobile]  │ │
│ │ ┌─────────────┐ │ │ │ 15   return (                   │ │ │ [🖥️ Desktop] │ │
│ │ │ React App   │ │ │ │ 16     <div className="app">    │ │ │             │ │
│ │ │ Vue.js      │ │ │ │ 17       <h1>Todo App</h1>      │ │ │             │ │
│ │ │ Next.js     │ │ │ │ 18       {/* ... */}            │ │ │             │ │
│ │ │ Express API │ │ │ │ 19     </div>                   │ │ │             │ │
│ │ └─────────────┘ │ │ │ 20   );                         │ │ │             │ │
│ └─────────────────┘ │ │ 21 };                           │ │ │             │ │
│                     │ └─────────────────────────────────┘ │ │             │ │
│                     │ [✅ No Errors] [⚠️ 0 Warnings]     │ │             │ │
│                     └─────────────────────────────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│ 🔧 [Console] [Terminal] [Problems] [Output] [Debug Console]                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ > npm start                                                             │ │
│ │ Compiled successfully!                                                  │ │
│ │ Local: http://localhost:3000                                            │ │
│ │ Network: http://*************:3000                                      │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

---

## 10. Sequence Diagrams

### 10.1 User Prompt to Code Generation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Frontend
    participant GW as API Gateway
    participant AO as Agent Orchestrator
    participant AI as Chutes AI
    participant API as Custom API
    participant DB as Database
    participant CS as Code Sandbox

    U->>UI: Submit prompt "Create React todo app"
    UI->>GW: POST /api/chat/message
    GW->>GW: Validate & authenticate
    GW->>AO: Route to active agent
    AO->>DB: Load agent config & context
    AO->>AI: Generate response with tools

    alt Agent needs external API
        AI->>AO: Request API call (weather data)
        AO->>API: Invoke custom API
        API-->>AO: Return weather data
        AO->>AI: Provide API response
    end

    AI-->>AO: Generated code + explanation
    AO->>CS: Create secure sandbox
    CS-->>AO: Sandbox URL
    AO->>DB: Save conversation & code
    AO-->>GW: Response with code & preview
    GW-->>UI: Stream response
    UI-->>U: Display code + live preview

    U->>UI: Click "Save to Project"
    UI->>GW: POST /api/projects/save
    GW->>DB: Create project files
    DB-->>GW: Confirm save
    GW-->>UI: Success response
    UI-->>U: Show success notification
```

### 10.2 Custom API Integration Flow

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Frontend
    participant GW as API Gateway
    participant AM as API Manager
    participant CM as Credential Manager
    participant DB as Database
    participant EXT as External API

    U->>UI: Open API Management
    UI->>GW: GET /api/apis/configurations
    GW->>DB: Fetch user's APIs
    DB-->>GW: Return API configs
    GW-->>UI: Display API list

    U->>UI: Add new API configuration
    UI->>UI: Show configuration form
    U->>UI: Fill API details + test
    UI->>GW: POST /api/apis/test
    GW->>AM: Validate configuration
    AM->>EXT: Test API call
    EXT-->>AM: Response
    AM-->>GW: Test result
    GW-->>UI: Show test status

    alt Test successful
        U->>UI: Save configuration
        UI->>GW: POST /api/apis/save
        GW->>CM: Encrypt & store credentials
        CM->>DB: Save encrypted API key
        GW->>DB: Save API configuration
        DB-->>GW: Confirm save
        GW-->>UI: Success response
        UI-->>U: Show success message
    else Test failed
        UI-->>U: Show error details
    end
```

### 10.3 Agent Switching with Context Preservation

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Frontend
    participant GW as API Gateway
    participant AO as Agent Orchestrator
    participant DB as Database
    participant A1 as Agent 1 (Current)
    participant A2 as Agent 2 (Target)

    U->>UI: Select different agent
    UI->>GW: POST /api/agents/switch
    GW->>AO: Request agent switch
    AO->>A1: Save current context
    A1->>DB: Persist workspace state
    AO->>DB: Update session agent_id
    AO->>A2: Load agent configuration
    A2->>DB: Retrieve conversation history
    A2->>A2: Initialize with context
    A2-->>AO: Ready with context
    AO-->>GW: Switch complete
    GW-->>UI: New agent active
    UI-->>U: Update interface

    U->>UI: Send message to new agent
    UI->>GW: POST /api/chat/message
    GW->>AO: Route to Agent 2
    AO->>A2: Process with preserved context
    A2-->>AO: Response with context awareness
    AO-->>GW: Contextual response
    GW-->>UI: Display response
    UI-->>U: Show agent response
```

---

## 11. Detailed Technical Specifications

### 11.1 API Integration Framework

**Dynamic API Client Generation:**
```typescript
class DynamicAPIClient {
  private apiConfig: APIConfiguration;
  private httpClient: AxiosInstance;

  constructor(config: APIConfiguration) {
    this.apiConfig = config;
    this.httpClient = this.createHttpClient();
  }

  private createHttpClient(): AxiosInstance {
    const client = axios.create({
      baseURL: this.apiConfig.baseUrl,
      timeout: 30000,
      headers: this.buildHeaders()
    });

    // Add request interceptor for authentication
    client.interceptors.request.use(this.addAuthentication.bind(this));

    // Add response interceptor for error handling
    client.interceptors.response.use(
      response => response,
      error => this.handleAPIError(error)
    );

    return client;
  }

  private buildHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'Shinmen-Platform/1.0'
    };

    if (this.apiConfig.authMethod.type === 'apikey' &&
        this.apiConfig.authMethod.headerName) {
      headers[this.apiConfig.authMethod.headerName] = this.getAPIKey();
    }

    return headers;
  }

  async invoke(request: APIRequest): Promise<APIResponse> {
    const rateLimitCheck = await this.checkRateLimit();
    if (!rateLimitCheck.allowed) {
      throw new APIError('Rate limit exceeded', 'RATE_LIMIT_EXCEEDED');
    }

    const httpRequest = this.buildRequest(request);
    const startTime = Date.now();

    try {
      const response = await this.httpClient.request(httpRequest);
      const executionTime = Date.now() - startTime;

      await this.logUsage(request, response, executionTime, true);

      return this.parseResponse(response);
    } catch (error) {
      const executionTime = Date.now() - startTime;
      await this.logUsage(request, null, executionTime, false, error.message);
      throw error;
    }
  }
}
```

**Function Calling Integration:**
```typescript
interface FunctionDefinition {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, JSONSchema>;
    required: string[];
  };
  apiMapping: {
    apiId: string;
    endpoint: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    parameterMapping: Record<string, string>;
  };
}

class FunctionCallHandler {
  async executeFunctionCall(
    functionCall: FunctionCall,
    context: AgentContext
  ): Promise<FunctionResult> {
    const functionDef = await this.getFunctionDefinition(functionCall.name);

    // Validate parameters
    const validation = this.validateParameters(
      functionCall.parameters,
      functionDef.parameters
    );

    if (!validation.valid) {
      throw new FunctionError('Invalid parameters', validation.errors);
    }

    // Map to API request
    const apiRequest = this.mapToAPIRequest(functionCall, functionDef);

    // Execute API call
    const apiClient = await this.getAPIClient(functionDef.apiMapping.apiId);
    const response = await apiClient.invoke(apiRequest);

    // Parse and return result
    return this.parseToFunctionResult(response, functionDef);
  }
}
```

### 11.2 Security Implementation Details

**Content Security Policy for Code Preview:**
```typescript
class SecurityPolicyManager {
  generateCSP(codeType: string): string {
    const basePolicy = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Required for code execution
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: blob:",
      "font-src 'self' data:",
      "connect-src 'self'",
      "frame-ancestors 'none'",
      "form-action 'none'"
    ];

    // Adjust policy based on code type
    switch (codeType) {
      case 'react':
      case 'javascript':
        basePolicy.push("worker-src 'self' blob:");
        break;
      case 'html':
        basePolicy.push("object-src 'none'");
        break;
    }

    return basePolicy.join('; ');
  }

  sanitizeCode(code: string, language: string): string {
    const sanitizer = new CodeSanitizer(language);

    // Remove dangerous patterns
    const dangerousPatterns = [
      /eval\s*\(/gi,
      /Function\s*\(/gi,
      /document\.write/gi,
      /innerHTML\s*=/gi,
      /outerHTML\s*=/gi
    ];

    let sanitized = code;
    dangerousPatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '/* REMOVED_FOR_SECURITY */');
    });

    return sanitizer.sanitize(sanitized);
  }
}
```

**Rate Limiting Implementation:**
```typescript
class RateLimiter {
  private redis: Redis;

  async checkLimit(
    key: string,
    limit: number,
    windowMs: number
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const window = Math.floor(now / windowMs);
    const redisKey = `rate_limit:${key}:${window}`;

    const current = await this.redis.incr(redisKey);

    if (current === 1) {
      await this.redis.expire(redisKey, Math.ceil(windowMs / 1000));
    }

    const remaining = Math.max(0, limit - current);
    const resetTime = (window + 1) * windowMs;

    return {
      allowed: current <= limit,
      remaining,
      resetTime,
      retryAfter: current > limit ? resetTime - now : null
    };
  }
}
```

---

This comprehensive design specification provides a detailed foundation for building an Interactive AI Agent Website with Custom API Integration that extends your existing Shinmen platform. The design leverages your current 12-agent architecture while adding powerful API integration capabilities, enhanced security measures, and a superior user experience.
